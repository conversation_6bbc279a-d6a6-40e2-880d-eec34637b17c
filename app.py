from flask import Flask, render_template, request, jsonify, send_file
import cv2
import numpy as np
import math
import os
import base64
from werkzeug.utils import secure_filename
import io
from PIL import Image

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['RESULTS_FOLDER'] = 'results'

# Create directories if they don't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['RESULTS_FOLDER'], exist_ok=True)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def detect_shape(contour):
    """Detect the shape of a contour"""
    epsilon = 0.02 * cv2.arcLength(contour, True)
    approx = cv2.approxPolyDP(contour, epsilon, True)
    vertices = len(approx)
    if vertices == 3:
        return "Triangle"
    elif vertices == 4:
        x, y, w, h = cv2.boundingRect(approx)
        ar = w / float(h)
        return "Square" if 0.95 <= ar <= 1.05 else "Rectangle"
    elif vertices > 6:
        return "Circle"
    return "Irregular"

def get_dimensions(contour, shape, scale_px_per_mm=10):
    """Get dimensions of a shape in mm"""
    if shape == "Circle":
        ((x, y), radius) = cv2.minEnclosingCircle(contour)
        radius_mm = radius / scale_px_per_mm
        area_mm2 = math.pi * radius_mm ** 2
        return (f"r={radius_mm:.1f}mm", area_mm2)
    else:
        x, y, w, h = cv2.boundingRect(contour)
        w_mm = w / scale_px_per_mm
        h_mm = h / scale_px_per_mm
        area_mm2 = w_mm * h_mm
        return (f"{w_mm:.1f}×{h_mm:.1f}mm", area_mm2)

def find_nearest_shape(contour, candidates):
    """Find the nearest matching shape for irregular contours"""
    min_score = float('inf')
    best_shape = "Irregular"
    for c, s in candidates:
        score = cv2.matchShapes(contour, c, 1, 0.0)
        if score < min_score:
            min_score = score
            best_shape = s
    return best_shape

def process_panel_image(image_path, scale_px_per_mm=10):
    """Process the panel image and return annotated result"""
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise FileNotFoundError("Image not found or invalid format.")
    
    # Preprocessing
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 130, 255, cv2.THRESH_BINARY_INV)
    contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    
    output = image.copy()
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.4
    font_color = (0, 0, 255)  # Red color
    thickness = 1
    
    # Build list of known shapes for irregular shape matching
    known_shapes = []
    for contour in contours:
        if cv2.contourArea(contour) < 50:  # Minimum area threshold
            continue
        shape = detect_shape(contour)
        if shape != "Irregular":
            known_shapes.append((contour, shape))
    
    # Process each contour
    shape_count = 0
    for contour in contours:
        if cv2.contourArea(contour) < 50:  # Minimum area threshold
            continue
        
        shape = detect_shape(contour)
        if shape == "Irregular":
            shape = find_nearest_shape(contour, known_shapes)
        
        # Calculate centroid
        M = cv2.moments(contour)
        if M["m00"] == 0:
            continue
        cX = int(M["m10"] / M["m00"])
        cY = int(M["m01"] / M["m00"])
        
        # Get dimensions
        dim_text, area = get_dimensions(contour, shape, scale_px_per_mm)
        
        # Draw contour and annotations
        cv2.drawContours(output, [contour], -1, (0, 255, 0), 2)
        
        # Add shape label
        cv2.putText(output, shape, (cX - 30, cY - 20), font, font_scale, font_color, thickness)
        # Add dimensions
        cv2.putText(output, dim_text, (cX - 30, cY), font, font_scale, font_color, thickness)
        # Add area
        cv2.putText(output, f"Area={area:.1f}mm²", (cX - 30, cY + 20), font, font_scale, font_color, thickness)
        
        # Detect corner radius for rectangles
        if shape == "Rectangle":
            x, y, w, h = cv2.boundingRect(contour)
            margin = max(5, int(min(w, h) * 0.15))
            
            corner_regions = {
                "TL": (x, y, binary[y:y+margin, x:x+margin]),
                "TR": (x+w-margin, y, binary[y:y+margin, x+w-margin:x+w]),
                "BL": (x, y+h-margin, binary[y+h-margin:y+h, x:x+margin]),
                "BR": (x+w-margin, y+h-margin, binary[y+h-margin:y+h, x+w-margin:x+w])
            }
            
            for label, (cx, cy, region) in corner_regions.items():
                if region.shape[0] < 2 or region.shape[1] < 2:
                    continue
                
                corner_contours, _ = cv2.findContours(region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                for corner in corner_contours:
                    if cv2.contourArea(corner) > 5:
                        ((r_x, r_y), radius) = cv2.minEnclosingCircle(corner)
                        radius_mm = radius / scale_px_per_mm
                        cv2.putText(output, f"{label} R={radius_mm:.1f}mm", 
                                  (cx + 2, cy + 10), font, font_scale * 0.8, font_color, thickness)
        
        shape_count += 1
    
    # Add title
    cv2.putText(output, "Shape Detection with Dimensions in mm", 
                (10, 30), font, 0.8, (255, 255, 255), 2)
    cv2.putText(output, f"Total Shapes Detected: {shape_count}", 
                (10, 60), font, 0.6, (255, 255, 255), 2)
    
    return output

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'No file selected'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        try:
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Get scale from form data (default to 10 px/mm)
            scale = float(request.form.get('scale', 10))
            
            # Process the image
            result_image = process_panel_image(filepath, scale)
            
            # Save result image
            result_filename = f"result_{filename}"
            result_path = os.path.join(app.config['RESULTS_FOLDER'], result_filename)
            cv2.imwrite(result_path, result_image)
            
            # Convert result image to base64 for display
            _, buffer = cv2.imencode('.jpg', result_image)
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return jsonify({
                'success': True,
                'result_image': f"data:image/jpeg;base64,{img_base64}",
                'message': 'Image processed successfully!'
            })
            
        except Exception as e:
            return jsonify({'error': f'Error processing image: {str(e)}'}), 500
    
    return jsonify({'error': 'Invalid file format. Please upload PNG, JPG, JPEG, BMP, or TIFF files.'}), 400

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
