from flask import Flask, render_template, request, jsonify, send_file
import cv2
import numpy as np
import math
import os
import base64
from werkzeug.utils import secure_filename
import io
from PIL import Image

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['RESULTS_FOLDER'] = 'results'

# Create directories if they don't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['RESULTS_FOLDER'], exist_ok=True)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def detect_shape(contour):
    """Detect the shape of a contour"""
    epsilon = 0.02 * cv2.arcLength(contour, True)
    approx = cv2.approxPolyDP(contour, epsilon, True)
    vertices = len(approx)
    if vertices == 3:
        return "Triangle"
    elif vertices == 4:
        x, y, w, h = cv2.boundingRect(approx)
        ar = w / float(h)
        return "Square" if 0.95 <= ar <= 1.05 else "Rectangle"
    elif vertices > 6:
        return "Circle"
    return "Irregular"

def get_dimensions(contour, shape, scale_px_per_mm=10):
    """Get dimensions of a shape in mm"""
    if shape == "Circle":
        ((x, y), radius) = cv2.minEnclosingCircle(contour)
        radius_mm = radius / scale_px_per_mm
        area_mm2 = math.pi * radius_mm ** 2
        return (f"r={radius_mm:.1f}mm", area_mm2)
    else:
        x, y, w, h = cv2.boundingRect(contour)
        w_mm = w / scale_px_per_mm
        h_mm = h / scale_px_per_mm
        area_mm2 = w_mm * h_mm
        return (f"{w_mm:.1f}×{h_mm:.1f}mm", area_mm2)

def find_nearest_shape(contour, candidates):
    """Find the nearest matching shape for irregular contours"""
    min_score = float('inf')
    best_shape = "Irregular"
    for c, s in candidates:
        score = cv2.matchShapes(contour, c, 1, 0.0)
        if score < min_score:
            min_score = score
            best_shape = s
    return best_shape

def draw_text_with_background(img, text, position, font, font_scale, text_color, bg_color, thickness=1, padding=5):
    """Draw text with a background rectangle for better visibility"""
    x, y = position

    # Get text size
    (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)

    # Calculate background rectangle coordinates
    bg_x1 = x - padding
    bg_y1 = y - text_height - padding
    bg_x2 = x + text_width + padding
    bg_y2 = y + baseline + padding

    # Draw background rectangle with rounded corners effect
    cv2.rectangle(img, (bg_x1, bg_y1), (bg_x2, bg_y2), bg_color, -1)
    cv2.rectangle(img, (bg_x1, bg_y1), (bg_x2, bg_y2), (255, 255, 255), 1)

    # Draw the text
    cv2.putText(img, text, (x, y), font, font_scale, text_color, thickness, cv2.LINE_AA)

    return text_height + padding * 2

def calculate_shape_properties(contour, shape, scale_px_per_mm):
    """Calculate comprehensive shape properties"""
    properties = {}

    # Basic measurements
    if shape == "Circle":
        ((x, y), radius) = cv2.minEnclosingCircle(contour)
        radius_mm = radius / scale_px_per_mm
        diameter_mm = radius_mm * 2
        area_mm2 = math.pi * radius_mm ** 2
        perimeter_mm = 2 * math.pi * radius_mm

        properties.update({
            'radius': radius_mm,
            'diameter': diameter_mm,
            'area': area_mm2,
            'perimeter': perimeter_mm,
            'center': (int(x), int(y))
        })
    else:
        x, y, w, h = cv2.boundingRect(contour)
        w_mm = w / scale_px_per_mm
        h_mm = h / scale_px_per_mm
        area_mm2 = w_mm * h_mm
        perimeter_mm = 2 * (w_mm + h_mm)

        properties.update({
            'width': w_mm,
            'height': h_mm,
            'area': area_mm2,
            'perimeter': perimeter_mm,
            'center': (x + w//2, y + h//2),
            'aspect_ratio': w_mm / h_mm if h_mm > 0 else 0
        })

    # Calculate actual contour area and perimeter
    actual_area = cv2.contourArea(contour) / (scale_px_per_mm ** 2)
    actual_perimeter = cv2.arcLength(contour, True) / scale_px_per_mm

    properties.update({
        'actual_area': actual_area,
        'actual_perimeter': actual_perimeter
    })

    return properties

def process_panel_image(image_path, scale_px_per_mm=10):
    """Process the panel image and return annotated result with enhanced features"""
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise FileNotFoundError("Image not found or invalid format.")

    # Get image dimensions for adaptive scaling
    height, width = image.shape[:2]

    # Adaptive preprocessing based on image characteristics
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Use adaptive thresholding for better results
    binary = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY_INV, 11, 2)

    # Apply morphological operations to clean up the binary image
    kernel = np.ones((3, 3), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

    contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    output = image.copy()

    # Adaptive font scaling based on image size
    base_font_scale = min(width, height) / 1000
    font_scale = max(0.4, min(1.2, base_font_scale))
    thickness = max(1, int(font_scale * 2))

    # Enhanced color scheme
    colors = {
        'contour': (0, 255, 0),      # Green for contours
        'text_bg': (0, 0, 0),        # Black background for text
        'text': (255, 255, 255),     # White text
        'accent': (255, 165, 0),     # Orange for highlights
        'dimension': (0, 255, 255),  # Cyan for dimensions
        'area': (255, 0, 255)        # Magenta for area
    }
    
    # Build list of known shapes for irregular shape matching
    known_shapes = []
    for contour in contours:
        if cv2.contourArea(contour) < 50:  # Minimum area threshold
            continue
        shape = detect_shape(contour)
        if shape != "Irregular":
            known_shapes.append((contour, shape))
    
    # Process each contour with enhanced annotations
    shape_count = 0
    shape_stats = {'Circle': 0, 'Rectangle': 0, 'Square': 0, 'Triangle': 0, 'Irregular': 0}

    for contour in contours:
        area_threshold = max(100, (width * height) / 10000)  # Adaptive threshold
        if cv2.contourArea(contour) < area_threshold:
            continue

        shape = detect_shape(contour)
        if shape == "Irregular":
            shape = find_nearest_shape(contour, known_shapes)

        shape_stats[shape] += 1

        # Calculate comprehensive properties
        properties = calculate_shape_properties(contour, shape, scale_px_per_mm)

        # Calculate centroid
        M = cv2.moments(contour)
        if M["m00"] == 0:
            continue
        cX = int(M["m10"] / M["m00"])
        cY = int(M["m01"] / M["m00"])

        # Draw enhanced contour with gradient effect
        cv2.drawContours(output, [contour], -1, colors['contour'], max(2, thickness))

        # Draw center point
        cv2.circle(output, (cX, cY), max(3, thickness), colors['accent'], -1)

        # Prepare annotation text with enhanced information
        if shape == "Circle":
            main_text = f"{shape}"
            dim_text = f"⌀{properties['diameter']:.1f}mm"
            area_text = f"A={properties['actual_area']:.1f}mm²"
        else:
            main_text = f"{shape}"
            if 'width' in properties and 'height' in properties:
                dim_text = f"{properties['width']:.1f}×{properties['height']:.1f}mm"
            else:
                dim_text = "Irregular dimensions"
            area_text = f"A={properties['actual_area']:.1f}mm²"

        # Position text to avoid overlap
        text_x = cX + 15
        text_y = cY - 15

        # Ensure text stays within image bounds
        if text_x + 150 > width:
            text_x = cX - 150
        if text_y - 60 < 0:
            text_y = cY + 60

        # Draw annotations with backgrounds
        font = cv2.FONT_HERSHEY_SIMPLEX
        y_offset = 0

        # Shape name
        y_offset += draw_text_with_background(output, main_text, (text_x, text_y + y_offset),
                                            font, font_scale * 1.1, colors['text'], colors['text_bg'], thickness)

        # Dimensions
        y_offset += draw_text_with_background(output, dim_text, (text_x, text_y + y_offset),
                                            font, font_scale, colors['dimension'], colors['text_bg'], thickness)

        # Area
        y_offset += draw_text_with_background(output, area_text, (text_x, text_y + y_offset),
                                            font, font_scale, colors['area'], colors['text_bg'], thickness)

        # Add perimeter for detailed analysis
        perim_text = f"P={properties['actual_perimeter']:.1f}mm"
        draw_text_with_background(output, perim_text, (text_x, text_y + y_offset),
                                font, font_scale * 0.9, colors['accent'], colors['text_bg'], thickness)
        
        # Enhanced corner detection for rectangles
        if shape in ["Rectangle", "Square"]:
            x, y, w, h = cv2.boundingRect(contour)
            margin = max(8, int(min(w, h) * 0.2))

            corner_regions = {
                "TL": (x, y, binary[y:y+margin, x:x+margin]),
                "TR": (x+w-margin, y, binary[y:y+margin, x+w-margin:x+w]),
                "BL": (x, y+h-margin, binary[y+h-margin:y+h, x:x+margin]),
                "BR": (x+w-margin, y+h-margin, binary[y+h-margin:y+h, x+w-margin:x+w])
            }

            corner_count = 0
            for label, (cx, cy, region) in corner_regions.items():
                if region.shape[0] < 2 or region.shape[1] < 2:
                    continue

                corner_contours, _ = cv2.findContours(region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                for corner in corner_contours:
                    if cv2.contourArea(corner) > 10:
                        ((r_x, r_y), radius) = cv2.minEnclosingCircle(corner)
                        radius_mm = radius / scale_px_per_mm

                        # Draw corner radius annotation
                        corner_text = f"R{radius_mm:.1f}"
                        corner_pos = (cx + 5, cy + 15)
                        draw_text_with_background(output, corner_text, corner_pos,
                                                font, font_scale * 0.7, colors['text'],
                                                colors['accent'], thickness)
                        corner_count += 1

        shape_count += 1
    
    # Create enhanced summary overlay
    overlay_height = 120
    overlay = np.zeros((overlay_height, width, 3), dtype=np.uint8)
    overlay[:] = (30, 30, 30)  # Dark background

    # Add gradient effect to overlay
    for i in range(overlay_height):
        alpha = 1.0 - (i / overlay_height) * 0.3
        overlay[i] = overlay[i] * alpha

    # Add summary information
    summary_font_scale = font_scale * 1.2
    summary_thickness = max(2, thickness)

    # Title
    title_text = "INDUSTRIAL PANEL ANALYSIS REPORT"
    cv2.putText(overlay, title_text, (20, 30), font, summary_font_scale, (255, 255, 255), summary_thickness)

    # Statistics
    stats_y = 55
    cv2.putText(overlay, f"Total Shapes: {shape_count}", (20, stats_y), font, font_scale, (0, 255, 255), thickness)
    cv2.putText(overlay, f"Scale: {scale_px_per_mm:.1f} px/mm", (200, stats_y), font, font_scale, (255, 165, 0), thickness)
    cv2.putText(overlay, f"Resolution: {width}x{height}", (400, stats_y), font, font_scale, (255, 0, 255), thickness)

    # Shape breakdown
    breakdown_y = 80
    x_pos = 20
    for shape_type, count in shape_stats.items():
        if count > 0:
            shape_text = f"{shape_type}: {count}"
            cv2.putText(overlay, shape_text, (x_pos, breakdown_y), font, font_scale * 0.8, (0, 255, 0), thickness)
            x_pos += len(shape_text) * 12 + 20

    # Add measurement grid reference
    grid_text = f"Grid Reference: 10mm = {scale_px_per_mm * 10:.0f}px"
    cv2.putText(overlay, grid_text, (20, 105), font, font_scale * 0.7, (200, 200, 200), thickness)

    # Blend overlay with main image
    alpha = 0.9
    output[:overlay_height] = cv2.addWeighted(output[:overlay_height], 1-alpha, overlay, alpha, 0)

    # Add measurement reference scale in corner
    scale_length_px = int(scale_px_per_mm * 50)  # 50mm reference
    scale_start_x = width - scale_length_px - 30
    scale_y = height - 50

    # Draw scale bar
    cv2.line(output, (scale_start_x, scale_y), (scale_start_x + scale_length_px, scale_y), (255, 255, 255), 3)
    cv2.line(output, (scale_start_x, scale_y - 5), (scale_start_x, scale_y + 5), (255, 255, 255), 2)
    cv2.line(output, (scale_start_x + scale_length_px, scale_y - 5), (scale_start_x + scale_length_px, scale_y + 5), (255, 255, 255), 2)

    # Scale label
    scale_label = "50mm"
    cv2.putText(output, scale_label, (scale_start_x + scale_length_px//2 - 20, scale_y - 10),
                font, font_scale, (255, 255, 255), thickness)

    return output, shape_stats

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'No file selected'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        try:
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Get scale from form data (default to 10 px/mm)
            scale = float(request.form.get('scale', 10))
            
            # Process the image
            result_image, shape_stats = process_panel_image(filepath, scale)

            # Save result image
            result_filename = f"result_{filename}"
            result_path = os.path.join(app.config['RESULTS_FOLDER'], result_filename)
            cv2.imwrite(result_path, result_image)

            # Convert result image to base64 for display
            _, buffer = cv2.imencode('.jpg', result_image)
            img_base64 = base64.b64encode(buffer).decode('utf-8')

            # Calculate total shapes
            total_shapes = sum(shape_stats.values())

            return jsonify({
                'success': True,
                'result_image': f"data:image/jpeg;base64,{img_base64}",
                'shape_stats': shape_stats,
                'total_shapes': total_shapes,
                'scale_used': scale,
                'message': 'Image processed successfully with enhanced analysis!'
            })
            
        except Exception as e:
            return jsonify({'error': f'Error processing image: {str(e)}'}), 500
    
    return jsonify({'error': 'Invalid file format. Please upload PNG, JPG, JPEG, BMP, or TIFF files.'}), 400

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
