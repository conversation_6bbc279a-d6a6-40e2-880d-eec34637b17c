# Industrial Panel Precision Measurement Web Application

A professional web application for analyzing CNC-milled metal panels, detecting shapes, and calculating precise measurements with dimensional annotations.

## Features

- **Professional Web Interface**: Modern, responsive design with drag-and-drop file upload
- **Shape Detection**: Automatically detects circles, rectangles, squares, triangles, and irregular shapes
- **Precise Measurements**: Calculates dimensions in millimeters with customizable scale
- **Corner Radius Detection**: Identifies and measures corner radii for rectangular shapes
- **Visual Annotations**: Provides annotated output images with labeled dimensions
- **Real-time Processing**: Fast image processing with progress indicators
- **Download Results**: Export annotated images for documentation

## Screenshots

### Input Panel
![Input Example](example_input.jpg)

### Output with Annotations
![Output Example](example_output.jpg)

## Installation

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Setup Instructions

1. **Clone or download the project files**
   ```bash
   # If using git
   git clone <repository-url>
   cd panel-measurement-app
   
   # Or simply ensure all files are in the same directory
   ```

2. **Create a virtual environment (recommended)**
   ```bash
   python -m venv venv
   
   # Activate virtual environment
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install required packages**
   ```bash
   pip install -r requirements.txt
   ```

4. **Create necessary directories**
   ```bash
   mkdir uploads results
   ```

5. **Run the application**
   ```bash
   python app.py
   ```

6. **Access the application**
   Open your web browser and navigate to:
   ```
   http://localhost:5000
   ```

## Usage

1. **Upload Image**: 
   - Drag and drop an image file or click to browse
   - Supported formats: PNG, JPG, JPEG, BMP, TIFF
   - Maximum file size: 16MB

2. **Set Scale**: 
   - Adjust the scale (pixels per mm) based on your image resolution
   - Default: 10 pixels per mm

3. **Analyze Panel**: 
   - Click "Analyze Panel" to process the image
   - Wait for processing to complete

4. **View Results**: 
   - Compare original and annotated images
   - Review detected shapes and measurements
   - Download the annotated result

## Configuration

### Scale Calibration
The scale setting determines the conversion from pixels to millimeters. To calibrate:

1. Measure a known dimension on your physical panel
2. Measure the same dimension in pixels on your image
3. Calculate: Scale = Pixels / Millimeters
4. Enter this value in the scale input field

### Processing Parameters
You can modify processing parameters in `app.py`:

- `cv2.threshold()` parameters for binary conversion
- Minimum contour area threshold
- Font size and positioning for annotations
- Corner detection sensitivity

## File Structure

```
panel-measurement-app/
├── app.py                          # Main Flask application
├── requirements.txt                # Python dependencies
├── README.md                      # This file
├── templates/
│   └── index.html                 # Main HTML template
├── static/
│   ├── style.css                  # CSS styling
│   └── script.js                  # JavaScript functionality
├── uploads/                       # Uploaded images (created automatically)
└── results/                       # Processed results (created automatically)
```

## Technical Details

### Image Processing Pipeline
1. **Preprocessing**: Convert to grayscale and apply binary threshold
2. **Contour Detection**: Find all contours using OpenCV
3. **Shape Classification**: Analyze vertices to classify shapes
4. **Dimension Calculation**: Calculate real-world measurements
5. **Annotation**: Add labels and measurements to output image

### Supported Shapes
- **Circles**: Detected by vertex count > 6, measured by radius
- **Rectangles**: Detected by 4 vertices with aspect ratio ≠ 1
- **Squares**: Detected by 4 vertices with aspect ratio ≈ 1
- **Triangles**: Detected by 3 vertices
- **Irregular**: Shapes that don't fit standard categories

### Web Framework
- **Backend**: Flask (Python web framework)
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Image Processing**: OpenCV, NumPy
- **File Handling**: Werkzeug, Pillow

## Troubleshooting

### Common Issues

1. **Import Error for cv2**
   ```bash
   pip install opencv-python
   ```

2. **Permission Errors**
   - Ensure the application has write permissions for uploads/ and results/ directories

3. **Large File Upload Issues**
   - Check that your image is under 16MB
   - Ensure stable internet connection

4. **Inaccurate Measurements**
   - Verify the scale setting matches your image resolution
   - Ensure good image quality with clear contrast

### Performance Optimization

- Use images with resolution appropriate for your measurement precision needs
- Ensure good lighting and contrast in source images
- Consider image compression for faster upload times

## Development

### Adding New Features
1. Modify `app.py` for backend functionality
2. Update `templates/index.html` for UI changes
3. Add styling in `static/style.css`
4. Implement client-side logic in `static/script.js`

### Testing
- Test with various image formats and sizes
- Verify measurements against known dimensions
- Test responsive design on different screen sizes

## License

This project is provided as-is for educational and commercial use.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all dependencies are correctly installed
3. Ensure proper file permissions and directory structure
