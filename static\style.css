/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
}

.header-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
}

.tagline {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 400;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Card Styles */
.upload-card, .loading-card, .results-card, .error-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Upload Section */
.upload-header {
    text-align: center;
    margin-bottom: 30px;
}

.upload-header h2 {
    font-size: 1.8rem;
    color: #2d3748;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.upload-header p {
    color: #718096;
    font-size: 1rem;
}

.upload-area {
    border: 3px dashed #cbd5e0;
    border-radius: 15px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 3rem;
    color: #cbd5e0;
    margin-bottom: 20px;
    transition: color 0.3s ease;
}

.upload-area:hover .upload-icon {
    color: #667eea;
}

.upload-content h3 {
    font-size: 1.3rem;
    color: #4a5568;
    margin-bottom: 10px;
    font-weight: 600;
}

.browse-text {
    color: #667eea;
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
}

/* File Preview */
.file-preview {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 10px;
    margin-top: 20px;
}

.file-preview img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
}

.file-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remove-file {
    background: #fed7d7;
    color: #e53e3e;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-file:hover {
    background: #feb2b2;
}

/* Settings Section */
.settings-section {
    margin: 30px 0;
    padding: 25px;
    background: #f7fafc;
    border-radius: 15px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-group label {
    font-weight: 600;
    color: #2d3748;
}

.setting-group input {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    max-width: 200px;
}

.setting-group input:focus {
    outline: none;
    border-color: #667eea;
}

.setting-group small {
    color: #718096;
    font-size: 0.9rem;
}

/* Buttons */
.upload-btn, .download-btn, .retry-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    margin-top: 20px;
}

.upload-btn:hover, .download-btn:hover, .retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.upload-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.download-btn {
    width: auto;
    margin: 0;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

/* Loading Section */
.loading-card {
    text-align: center;
}

.loading-spinner {
    margin-bottom: 30px;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 20px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    width: 0%;
    animation: progress 3s ease-in-out;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Results Section */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.results-header h2 {
    font-size: 1.8rem;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 10px;
}

.image-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.image-container {
    text-align: center;
}

.image-container h3 {
    margin-bottom: 15px;
    color: #4a5568;
    font-weight: 600;
}

.image-container img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #e2e8f0;
}

/* Analysis Summary */
.analysis-summary h3 {
    margin-bottom: 20px;
    color: #2d3748;
    font-weight: 600;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.summary-item i {
    font-size: 1.5rem;
    color: #667eea;
}

.summary-item .label {
    display: block;
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

.summary-item .value {
    display: block;
    font-size: 1.2rem;
    color: #2d3748;
    font-weight: 700;
}

/* Error Section */
.error-card {
    text-align: center;
    color: #e53e3e;
}

.error-card i {
    font-size: 3rem;
    margin-bottom: 20px;
}

.error-card h3 {
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.retry-btn {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    max-width: 200px;
    margin: 20px auto 0;
}

/* Footer */
.footer {
    margin-top: 60px;
    text-align: center;
}

.footer-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    color: white;
}

.footer-links {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header-content {
        padding: 30px 20px;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .upload-card, .loading-card, .results-card, .error-card {
        padding: 25px;
    }
    
    .image-comparison {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 15px;
    }
}
