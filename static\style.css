/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
    background-attachment: fixed;
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
    position: relative;
}

.header-content {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 50px 40px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.header-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    position: relative;
}

.logo i {
    font-size: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(102, 126, 234, 0.3));
}

.logo h1 {
    font-size: 3rem;
    font-weight: 800;
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: -0.02em;
}

.tagline {
    font-size: 1.2rem;
    color: #718096;
    font-weight: 500;
    margin-top: 10px;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Card Styles */
.upload-card, .loading-card, .results-card, .error-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 50px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.upload-card::before, .loading-card::before, .results-card::before, .error-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.upload-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* Upload Section */
.upload-header {
    text-align: center;
    margin-bottom: 30px;
}

.upload-header h2 {
    font-size: 1.8rem;
    color: #2d3748;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.upload-header p {
    color: #718096;
    font-size: 1rem;
}

.upload-area {
    border: 3px dashed #e2e8f0;
    border-radius: 20px;
    padding: 80px 30px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 17px;
}

.upload-area:hover {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.upload-area:hover::before {
    opacity: 1;
}

.upload-area.dragover {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
    transform: scale(1.02) translateY(-2px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
    border-style: solid;
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 3rem;
    color: #cbd5e0;
    margin-bottom: 20px;
    transition: color 0.3s ease;
}

.upload-area:hover .upload-icon {
    color: #667eea;
}

.upload-content h3 {
    font-size: 1.3rem;
    color: #4a5568;
    margin-bottom: 10px;
    font-weight: 600;
}

.browse-text {
    color: #667eea;
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
}

/* File Preview */
.file-preview {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 10px;
    margin-top: 20px;
}

.file-preview img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
}

.file-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remove-file {
    background: #fed7d7;
    color: #e53e3e;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-file:hover {
    background: #feb2b2;
}

/* Settings Section */
.settings-section {
    margin: 30px 0;
    padding: 30px;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.settings-section h3 {
    margin-bottom: 25px;
    color: #2d3748;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-group label {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.95rem;
}

.setting-group input, .setting-group select {
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.setting-group input:focus, .setting-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.setting-group small {
    color: #718096;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Calibration Helper */
.calibration-helper {
    background: rgba(255, 255, 255, 0.7);
    padding: 20px;
    border-radius: 15px;
    border-left: 4px solid #667eea;
}

.calibration-helper h4 {
    color: #2d3748;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.calibration-helper ol {
    margin: 15px 0;
    padding-left: 20px;
    color: #4a5568;
}

.calibration-helper li {
    margin-bottom: 5px;
}

.quick-scales {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.quick-scales span {
    font-weight: 600;
    color: #4a5568;
}

.scale-preset {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.scale-preset:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Buttons */
.upload-btn, .download-btn, .retry-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    margin-top: 20px;
}

.upload-btn:hover, .download-btn:hover, .retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.upload-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.download-btn {
    width: auto;
    margin: 0;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

/* Loading Section */
.loading-card {
    text-align: center;
}

.loading-spinner {
    margin-bottom: 30px;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 20px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    width: 0%;
    animation: progress 3s ease-in-out;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Results Section */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.results-header h2 {
    font-size: 1.8rem;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 10px;
}

.image-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.image-container {
    text-align: center;
}

.image-container h3 {
    margin-bottom: 15px;
    color: #4a5568;
    font-weight: 600;
}

.image-container img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 3px solid #e2e8f0;
}

/* Analysis Summary */
.analysis-summary h3 {
    margin-bottom: 25px;
    color: #2d3748;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 25px;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 15px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.summary-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.summary-item i {
    font-size: 1.8rem;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 10px;
    border-radius: 10px;
}

.summary-item .label {
    display: block;
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
    margin-bottom: 2px;
}

.summary-item .value {
    display: block;
    font-size: 1.3rem;
    color: #2d3748;
    font-weight: 700;
}

/* Shape Breakdown */
.shape-breakdown {
    margin: 30px 0;
    padding: 25px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.shape-breakdown h4 {
    color: #2d3748;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.shape-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.shape-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 10px;
    border-left: 3px solid #667eea;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.shape-stat-item .shape-name {
    font-weight: 600;
    color: #4a5568;
}

.shape-stat-item .shape-count {
    font-weight: 700;
    color: #667eea;
    font-size: 1.1rem;
}

/* Measurement Accuracy */
.measurement-accuracy {
    margin: 30px 0;
    padding: 25px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    border: 1px solid rgba(255, 165, 0, 0.2);
}

.measurement-accuracy h4 {
    color: #2d3748;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.accuracy-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.accuracy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: 10px;
    border-left: 3px solid #ff9500;
}

.accuracy-item .label {
    font-weight: 500;
    color: #4a5568;
}

.accuracy-item .value {
    font-weight: 600;
    color: #ff9500;
}

/* Export Options */
.export-options {
    margin: 30px 0;
    padding: 25px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    border: 1px solid rgba(72, 187, 120, 0.2);
}

.export-options h4 {
    color: #2d3748;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.export-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.export-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
}

/* Error Section */
.error-card {
    text-align: center;
    color: #e53e3e;
}

.error-card i {
    font-size: 3rem;
    margin-bottom: 20px;
}

.error-card h3 {
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.retry-btn {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    max-width: 200px;
    margin: 20px auto 0;
}

/* Footer */
.footer {
    margin-top: 60px;
    text-align: center;
}

.footer-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    color: white;
}

.footer-links {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header-content {
        padding: 30px 20px;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .upload-card, .loading-card, .results-card, .error-card {
        padding: 25px;
    }
    
    .image-comparison {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 15px;
    }
}
