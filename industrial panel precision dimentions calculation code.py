import cv2
import numpy as np
import math
from google.colab.patches import cv2_imshow

# Load image
image = cv2.imread("/content/this_result.jpg")
if image is None:
    raise FileNotFoundError("Image not found. Check path.")

# Scale (you can change this if you want real-world cm)
scale_px_per_cm = 40

# Preprocessing
gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
_, binary = cv2.threshold(gray, 130, 255, cv2.THRESH_BINARY_INV)
contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

output = image.copy()
font = cv2.FONT_HERSHEY_SIMPLEX
font_scale = 0.3
font_color = (0, 0, 255)
thickness = 1

# Shape detection helper
def detect_shape(contour):
    epsilon = 0.02 * cv2.arcLength(contour, True)
    approx = cv2.approxPolyDP(contour, epsilon, True)
    vertices = len(approx)
    if vertices == 3:
        return "Triangle"
    elif vertices == 4:
        x, y, w, h = cv2.boundingRect(approx)
        ar = w / float(h)
        return "Square" if 0.95 <= ar <= 1.05 else "Rectangle"
    elif vertices > 6:
        return "Circle"
    return "Irregular"

# Dimension helper
def get_dimensions(contour, shape):
    if shape == "Circle":
        ((x, y), radius) = cv2.minEnclosingCircle(contour)
        return ("r={:.1f}px".format(radius), math.pi * radius ** 2)
    else:
        x, y, w, h = cv2.boundingRect(contour)
        area = w * h
        return ("{}×{}px".format(w, h), area)

# For better labeling of irregulars
def find_nearest_shape(contour, candidates):
    min_score = float('inf')
    best_shape = "Irregular"
    for c, s in candidates:
        score = cv2.matchShapes(contour, c, 1, 0.0)
        if score < min_score:
            min_score = score
            best_shape = s
    return best_shape

# Build list of known shapes
known_shapes = []
for contour in contours:
    if cv2.contourArea(contour) < 10:
        continue
    shape = detect_shape(contour)
    if shape != "Irregular":
        known_shapes.append((contour, shape))

# --- Main loop for drawing and labeling ---
for contour in contours:
    if cv2.contourArea(contour) < 10:
        continue

    shape = detect_shape(contour)
    if shape == "Irregular":
        shape = find_nearest_shape(contour, known_shapes)

    M = cv2.moments(contour)
    if M["m00"] == 0:
        continue
    cX = int(M["m10"] / M["m00"])
    cY = int(M["m01"] / M["m00"])

    dim_text, area = get_dimensions(contour, shape)

    cv2.drawContours(output, [contour], -1, (0, 255, 0), 1)
    cv2.putText(output, shape, (cX - 10, cY - 10), font, font_scale, font_color, thickness)
    cv2.putText(output, dim_text, (cX - 10, cY + 5), font, font_scale, font_color, thickness)
    cv2.putText(output, f"a={area:.1f}", (cX - 10, cY + 18), font, font_scale, font_color, thickness)

    # --- Detect corner radius if it's a rectangle ---
    if shape == "Rectangle":
        x, y, w, h = cv2.boundingRect(contour)
        margin = max(5, int(min(w, h) * 0.15))

        corner_regions = {
            "TL": (x, y, binary[y:y+margin, x:x+margin]),
            "TR": (x+w-margin, y, binary[y:y+margin, x+w-margin:x+w]),
            "BL": (x, y+h-margin, binary[y+h-margin:y+h, x:x+margin]),
            "BR": (x+w-margin, y+h-margin, binary[y+h-margin:y+h, x+w-margin:x+w])
        }

        for label, (cx, cy, region) in corner_regions.items():
            if region.shape[0] < 2 or region.shape[1] < 2:
                continue

            corner_contours, _ = cv2.findContours(region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            for corner in corner_contours:
                if cv2.contourArea(corner) > 5:
                    ((r_x, r_y), radius) = cv2.minEnclosingCircle(corner)
                    cv2.putText(output, f"{label} R={radius:.1f}", (cx + 2, cy + 10), font, font_scale, font_color, thickness)

# Show final output
cv2_imshow(output)
