<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Industrial Panel Precision Measurement</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-industry"></i>
                    <h1>Panel Precision</h1>
                </div>
                <p class="tagline">Advanced CNC Panel Measurement & Analysis</p>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section">
                <div class="upload-card">
                    <div class="upload-header">
                        <h2><i class="fas fa-upload"></i> Upload Panel Image</h2>
                        <p>Upload an image of your CNC-milled metal panel for precise measurement analysis</p>
                    </div>
                    
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h3>Drag & Drop your image here</h3>
                                <p>or <span class="browse-text">browse files</span></p>
                                <input type="file" id="fileInput" name="file" accept=".png,.jpg,.jpeg,.bmp,.tiff" hidden>
                            </div>
                            <div class="file-preview" id="filePreview" style="display: none;">
                                <img id="previewImage" src="" alt="Preview">
                                <div class="file-info">
                                    <p id="fileName"></p>
                                    <button type="button" class="remove-file" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="settings-section">
                            <h3><i class="fas fa-cog"></i> Analysis Settings</h3>
                            <div class="settings-grid">
                                <div class="setting-group">
                                    <label for="scaleInput">Scale (pixels per mm):</label>
                                    <input type="number" id="scaleInput" name="scale" value="10" min="1" max="100" step="0.1">
                                    <small>Adjust based on your image resolution and actual panel size</small>
                                </div>
                                <div class="setting-group">
                                    <label for="thresholdInput">Detection Sensitivity:</label>
                                    <select id="thresholdInput" name="threshold">
                                        <option value="low">Low (Large shapes only)</option>
                                        <option value="medium" selected>Medium (Balanced)</option>
                                        <option value="high">High (All details)</option>
                                    </select>
                                    <small>Adjust sensitivity for shape detection</small>
                                </div>
                                <div class="setting-group">
                                    <label for="annotationStyle">Annotation Style:</label>
                                    <select id="annotationStyle" name="annotation_style">
                                        <option value="detailed" selected>Detailed (All measurements)</option>
                                        <option value="minimal">Minimal (Shape names only)</option>
                                        <option value="technical">Technical (Engineering style)</option>
                                    </select>
                                    <small>Choose annotation detail level</small>
                                </div>
                            </div>

                            <div class="calibration-helper">
                                <h4><i class="fas fa-ruler"></i> Scale Calibration Helper</h4>
                                <p>To calibrate the scale accurately:</p>
                                <ol>
                                    <li>Measure a known dimension on your physical panel</li>
                                    <li>Count the pixels for the same dimension in your image</li>
                                    <li>Calculate: Scale = Pixels ÷ Millimeters</li>
                                </ol>
                                <div class="quick-scales">
                                    <span>Quick presets:</span>
                                    <button type="button" class="scale-preset" data-scale="5">5 px/mm</button>
                                    <button type="button" class="scale-preset" data-scale="10">10 px/mm</button>
                                    <button type="button" class="scale-preset" data-scale="15">15 px/mm</button>
                                    <button type="button" class="scale-preset" data-scale="20">20 px/mm</button>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="upload-btn" id="uploadBtn" disabled>
                            <i class="fas fa-cogs"></i>
                            <span>Analyze Panel</span>
                        </button>
                    </form>
                </div>
            </section>

            <!-- Loading Section -->
            <section class="loading-section" id="loadingSection" style="display: none;">
                <div class="loading-card">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                    <h3>Processing Your Panel Image</h3>
                    <p>Analyzing shapes, calculating dimensions, and detecting features...</p>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-card">
                    <div class="results-header">
                        <h2><i class="fas fa-chart-line"></i> Analysis Results</h2>
                        <button class="download-btn" id="downloadBtn">
                            <i class="fas fa-download"></i>
                            Download Result
                        </button>
                    </div>
                    
                    <div class="results-content">
                        <div class="image-comparison">
                            <div class="image-container">
                                <h3>Original Image</h3>
                                <img id="originalImage" src="" alt="Original Panel">
                            </div>
                            <div class="image-container">
                                <h3>Annotated Result</h3>
                                <img id="resultImage" src="" alt="Analyzed Panel">
                            </div>
                        </div>
                        
                        <div class="analysis-summary">
                            <h3><i class="fas fa-chart-bar"></i> Detailed Analysis Summary</h3>
                            <div class="summary-grid">
                                <div class="summary-item">
                                    <i class="fas fa-shapes"></i>
                                    <div>
                                        <span class="label">Total Shapes</span>
                                        <span class="value" id="shapesCount">-</span>
                                    </div>
                                </div>
                                <div class="summary-item">
                                    <i class="fas fa-ruler"></i>
                                    <div>
                                        <span class="label">Scale Used</span>
                                        <span class="value" id="scaleUsed">-</span>
                                    </div>
                                </div>
                                <div class="summary-item">
                                    <i class="fas fa-clock"></i>
                                    <div>
                                        <span class="label">Processing Time</span>
                                        <span class="value" id="processingTime">-</span>
                                    </div>
                                </div>
                                <div class="summary-item">
                                    <i class="fas fa-file-image"></i>
                                    <div>
                                        <span class="label">Image Resolution</span>
                                        <span class="value" id="imageResolution">-</span>
                                    </div>
                                </div>
                            </div>

                            <div class="shape-breakdown">
                                <h4><i class="fas fa-pie-chart"></i> Shape Distribution</h4>
                                <div class="shape-stats" id="shapeStats">
                                    <!-- Shape statistics will be populated here -->
                                </div>
                            </div>

                            <div class="measurement-accuracy">
                                <h4><i class="fas fa-crosshairs"></i> Measurement Accuracy</h4>
                                <div class="accuracy-info">
                                    <div class="accuracy-item">
                                        <span class="label">Theoretical Precision:</span>
                                        <span class="value" id="theoreticalPrecision">-</span>
                                    </div>
                                    <div class="accuracy-item">
                                        <span class="label">Recommended Use:</span>
                                        <span class="value" id="recommendedUse">-</span>
                                    </div>
                                </div>
                            </div>

                            <div class="export-options">
                                <h4><i class="fas fa-download"></i> Export Options</h4>
                                <div class="export-buttons">
                                    <button class="export-btn" id="exportPDF">
                                        <i class="fas fa-file-pdf"></i>
                                        Export PDF Report
                                    </button>
                                    <button class="export-btn" id="exportCSV">
                                        <i class="fas fa-file-csv"></i>
                                        Export CSV Data
                                    </button>
                                    <button class="export-btn" id="exportJSON">
                                        <i class="fas fa-file-code"></i>
                                        Export JSON Data
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Error Section -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-card">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Processing Error</h3>
                    <p id="errorMessage">An error occurred while processing your image.</p>
                    <button class="retry-btn" id="retryBtn">
                        <i class="fas fa-redo"></i>
                        Try Again
                    </button>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2024 Panel Precision. Advanced industrial measurement solutions.</p>
                <div class="footer-links">
                    <a href="#"><i class="fas fa-info-circle"></i> About</a>
                    <a href="#"><i class="fas fa-envelope"></i> Contact</a>
                    <a href="#"><i class="fas fa-question-circle"></i> Help</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
