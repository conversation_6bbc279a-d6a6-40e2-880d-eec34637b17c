// DOM Elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const filePreview = document.getElementById('filePreview');
const previewImage = document.getElementById('previewImage');
const fileName = document.getElementById('fileName');
const removeFile = document.getElementById('removeFile');
const uploadForm = document.getElementById('uploadForm');
const uploadBtn = document.getElementById('uploadBtn');
const scaleInput = document.getElementById('scaleInput');

// Section elements
const uploadSection = document.querySelector('.upload-section');
const loadingSection = document.getElementById('loadingSection');
const resultsSection = document.getElementById('resultsSection');
const errorSection = document.getElementById('errorSection');

// Result elements
const originalImage = document.getElementById('originalImage');
const resultImage = document.getElementById('resultImage');
const shapesCount = document.getElementById('shapesCount');
const scaleUsed = document.getElementById('scaleUsed');
const processingTime = document.getElementById('processingTime');
const downloadBtn = document.getElementById('downloadBtn');
const retryBtn = document.getElementById('retryBtn');
const errorMessage = document.getElementById('errorMessage');

// State variables
let selectedFile = null;
let startTime = null;
let resultImageData = null;

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // Upload area events
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    
    // File input change
    fileInput.addEventListener('change', handleFileSelect);
    
    // Remove file button
    removeFile.addEventListener('click', clearFileSelection);
    
    // Form submission
    uploadForm.addEventListener('submit', handleFormSubmit);
    
    // Retry button
    retryBtn.addEventListener('click', resetToUpload);
    
    // Download button
    downloadBtn.addEventListener('click', downloadResult);
    
    // Scale input validation
    scaleInput.addEventListener('input', validateScale);
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    // Validate file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/bmp', 'image/tiff'];
    if (!allowedTypes.includes(file.type)) {
        showError('Please select a valid image file (PNG, JPG, JPEG, BMP, or TIFF).');
        return;
    }
    
    // Validate file size (16MB max)
    const maxSize = 16 * 1024 * 1024;
    if (file.size > maxSize) {
        showError('File size must be less than 16MB.');
        return;
    }
    
    selectedFile = file;
    displayFilePreview(file);
    enableUploadButton();
}

function displayFilePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImage.src = e.target.result;
        fileName.textContent = file.name;
        
        // Hide upload content and show preview
        document.querySelector('.upload-content').style.display = 'none';
        filePreview.style.display = 'flex';
        
        // Store original image for results
        originalImage.src = e.target.result;
    };
    reader.readAsDataURL(file);
}

function clearFileSelection() {
    selectedFile = null;
    fileInput.value = '';
    
    // Reset upload area
    document.querySelector('.upload-content').style.display = 'block';
    filePreview.style.display = 'none';
    
    disableUploadButton();
}

function enableUploadButton() {
    uploadBtn.disabled = false;
}

function disableUploadButton() {
    uploadBtn.disabled = true;
}

function validateScale() {
    const value = parseFloat(scaleInput.value);
    if (isNaN(value) || value < 1 || value > 100) {
        scaleInput.setCustomValidity('Scale must be between 1 and 100');
    } else {
        scaleInput.setCustomValidity('');
    }
}

function handleFormSubmit(e) {
    e.preventDefault();
    
    if (!selectedFile) {
        showError('Please select an image file first.');
        return;
    }
    
    const scale = parseFloat(scaleInput.value);
    if (isNaN(scale) || scale < 1 || scale > 100) {
        showError('Please enter a valid scale value between 1 and 100.');
        return;
    }
    
    uploadImage();
}

function uploadImage() {
    startTime = Date.now();
    showLoadingSection();
    
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('scale', scaleInput.value);
    
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showResults(data);
        } else {
            showError(data.error || 'An error occurred while processing the image.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('Network error. Please check your connection and try again.');
    });
}

function showLoadingSection() {
    hideAllSections();
    loadingSection.style.display = 'block';
    
    // Animate progress bar
    const progressFill = document.getElementById('progressFill');
    progressFill.style.animation = 'none';
    setTimeout(() => {
        progressFill.style.animation = 'progress 3s ease-in-out';
    }, 100);
}

function showResults(data) {
    const endTime = Date.now();
    const processingTimeMs = endTime - startTime;
    
    // Set result data
    resultImage.src = data.result_image;
    resultImageData = data.result_image;
    
    // Extract shapes count from the result (this would need to be sent from backend)
    shapesCount.textContent = 'Multiple'; // Placeholder
    scaleUsed.textContent = `${scaleInput.value} px/mm`;
    processingTime.textContent = `${(processingTimeMs / 1000).toFixed(1)}s`;
    
    hideAllSections();
    resultsSection.style.display = 'block';
    
    // Smooth scroll to results
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

function showError(message) {
    errorMessage.textContent = message;
    hideAllSections();
    errorSection.style.display = 'block';
}

function hideAllSections() {
    loadingSection.style.display = 'none';
    resultsSection.style.display = 'none';
    errorSection.style.display = 'none';
}

function resetToUpload() {
    hideAllSections();
    uploadSection.style.display = 'block';
    
    // Scroll back to upload section
    uploadSection.scrollIntoView({ behavior: 'smooth' });
}

function downloadResult() {
    if (!resultImageData) {
        showError('No result image available for download.');
        return;
    }
    
    // Convert base64 to blob and download
    const link = document.createElement('a');
    link.href = resultImageData;
    link.download = `panel_analysis_result_${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Add some visual feedback for better UX
function addRippleEffect(element, e) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Add ripple effect to buttons
document.querySelectorAll('button').forEach(button => {
    button.addEventListener('click', function(e) {
        if (!this.disabled) {
            addRippleEffect(this, e);
        }
    });
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    button {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
