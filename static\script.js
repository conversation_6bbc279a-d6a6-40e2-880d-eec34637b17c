// DOM Elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const filePreview = document.getElementById('filePreview');
const previewImage = document.getElementById('previewImage');
const fileName = document.getElementById('fileName');
const removeFile = document.getElementById('removeFile');
const uploadForm = document.getElementById('uploadForm');
const uploadBtn = document.getElementById('uploadBtn');
const scaleInput = document.getElementById('scaleInput');

// Section elements
const uploadSection = document.querySelector('.upload-section');
const loadingSection = document.getElementById('loadingSection');
const resultsSection = document.getElementById('resultsSection');
const errorSection = document.getElementById('errorSection');

// Result elements
const originalImage = document.getElementById('originalImage');
const resultImage = document.getElementById('resultImage');
const shapesCount = document.getElementById('shapesCount');
const scaleUsed = document.getElementById('scaleUsed');
const processingTime = document.getElementById('processingTime');
const downloadBtn = document.getElementById('downloadBtn');
const retryBtn = document.getElementById('retryBtn');
const errorMessage = document.getElementById('errorMessage');

// State variables
let selectedFile = null;
let startTime = null;
let resultImageData = null;

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // Upload area events
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);

    // File input change
    fileInput.addEventListener('change', handleFileSelect);

    // Remove file button
    removeFile.addEventListener('click', clearFileSelection);

    // Form submission
    uploadForm.addEventListener('submit', handleFormSubmit);

    // Retry button
    retryBtn.addEventListener('click', resetToUpload);

    // Download button
    downloadBtn.addEventListener('click', downloadResult);

    // Scale input validation
    scaleInput.addEventListener('input', validateScale);

    // Scale preset buttons
    document.querySelectorAll('.scale-preset').forEach(btn => {
        btn.addEventListener('click', function() {
            const scale = this.getAttribute('data-scale');
            scaleInput.value = scale;
            validateScale();

            // Visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // Export buttons
    const exportPDF = document.getElementById('exportPDF');
    const exportCSV = document.getElementById('exportCSV');
    const exportJSON = document.getElementById('exportJSON');

    if (exportPDF) exportPDF.addEventListener('click', () => exportData('pdf'));
    if (exportCSV) exportCSV.addEventListener('click', () => exportData('csv'));
    if (exportJSON) exportJSON.addEventListener('click', () => exportData('json'));
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    // Validate file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/bmp', 'image/tiff'];
    if (!allowedTypes.includes(file.type)) {
        showError('Please select a valid image file (PNG, JPG, JPEG, BMP, or TIFF).');
        return;
    }
    
    // Validate file size (16MB max)
    const maxSize = 16 * 1024 * 1024;
    if (file.size > maxSize) {
        showError('File size must be less than 16MB.');
        return;
    }
    
    selectedFile = file;
    displayFilePreview(file);
    enableUploadButton();
}

function displayFilePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImage.src = e.target.result;
        fileName.textContent = file.name;
        
        // Hide upload content and show preview
        document.querySelector('.upload-content').style.display = 'none';
        filePreview.style.display = 'flex';
        
        // Store original image for results
        originalImage.src = e.target.result;
    };
    reader.readAsDataURL(file);
}

function clearFileSelection() {
    selectedFile = null;
    fileInput.value = '';
    
    // Reset upload area
    document.querySelector('.upload-content').style.display = 'block';
    filePreview.style.display = 'none';
    
    disableUploadButton();
}

function enableUploadButton() {
    uploadBtn.disabled = false;
}

function disableUploadButton() {
    uploadBtn.disabled = true;
}

function validateScale() {
    const value = parseFloat(scaleInput.value);
    if (isNaN(value) || value < 1 || value > 100) {
        scaleInput.setCustomValidity('Scale must be between 1 and 100');
    } else {
        scaleInput.setCustomValidity('');
    }
}

function handleFormSubmit(e) {
    e.preventDefault();
    
    if (!selectedFile) {
        showError('Please select an image file first.');
        return;
    }
    
    const scale = parseFloat(scaleInput.value);
    if (isNaN(scale) || scale < 1 || scale > 100) {
        showError('Please enter a valid scale value between 1 and 100.');
        return;
    }
    
    uploadImage();
}

function uploadImage() {
    startTime = Date.now();
    showLoadingSection();
    
    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('scale', scaleInput.value);
    
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showResults(data);
        } else {
            showError(data.error || 'An error occurred while processing the image.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('Network error. Please check your connection and try again.');
    });
}

function showLoadingSection() {
    hideAllSections();
    loadingSection.style.display = 'block';
    
    // Animate progress bar
    const progressFill = document.getElementById('progressFill');
    progressFill.style.animation = 'none';
    setTimeout(() => {
        progressFill.style.animation = 'progress 3s ease-in-out';
    }, 100);
}

function showResults(data) {
    const endTime = Date.now();
    const processingTimeMs = endTime - startTime;

    // Set result data
    resultImage.src = data.result_image;
    resultImageData = data.result_image;

    // Update summary statistics
    shapesCount.textContent = data.total_shapes || 0;
    scaleUsed.textContent = `${data.scale_used} px/mm`;
    processingTime.textContent = `${(processingTimeMs / 1000).toFixed(1)}s`;

    // Get image dimensions from the original image
    const img = new Image();
    img.onload = function() {
        document.getElementById('imageResolution').textContent = `${this.width}×${this.height}px`;

        // Calculate measurement accuracy
        const precision = 1 / data.scale_used;
        document.getElementById('theoreticalPrecision').textContent = `±${precision.toFixed(2)}mm`;

        // Determine recommended use based on precision
        let recommendedUse = '';
        if (precision < 0.1) {
            recommendedUse = 'High precision manufacturing';
        } else if (precision < 0.5) {
            recommendedUse = 'Standard manufacturing';
        } else {
            recommendedUse = 'General measurements';
        }
        document.getElementById('recommendedUse').textContent = recommendedUse;
    };
    img.src = originalImage.src;

    // Update shape breakdown
    updateShapeBreakdown(data.shape_stats || {});

    hideAllSections();
    resultsSection.style.display = 'block';

    // Smooth scroll to results
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

function updateShapeBreakdown(shapeStats) {
    const shapeStatsContainer = document.getElementById('shapeStats');
    shapeStatsContainer.innerHTML = '';

    const shapeIcons = {
        'Circle': 'fas fa-circle',
        'Rectangle': 'fas fa-square',
        'Square': 'fas fa-stop',
        'Triangle': 'fas fa-play',
        'Irregular': 'fas fa-puzzle-piece'
    };

    const shapeColors = {
        'Circle': '#667eea',
        'Rectangle': '#48bb78',
        'Square': '#ed8936',
        'Triangle': '#9f7aea',
        'Irregular': '#38b2ac'
    };

    Object.entries(shapeStats).forEach(([shape, count]) => {
        if (count > 0) {
            const statItem = document.createElement('div');
            statItem.className = 'shape-stat-item';
            statItem.style.borderLeftColor = shapeColors[shape] || '#667eea';

            statItem.innerHTML = `
                <div class="shape-info">
                    <i class="${shapeIcons[shape] || 'fas fa-shapes'}" style="color: ${shapeColors[shape] || '#667eea'}; margin-right: 8px;"></i>
                    <span class="shape-name">${shape}</span>
                </div>
                <span class="shape-count">${count}</span>
            `;

            shapeStatsContainer.appendChild(statItem);
        }
    });

    if (shapeStatsContainer.children.length === 0) {
        shapeStatsContainer.innerHTML = '<p style="text-align: center; color: #718096;">No shapes detected</p>';
    }
}

function showError(message) {
    errorMessage.textContent = message;
    hideAllSections();
    errorSection.style.display = 'block';
}

function hideAllSections() {
    loadingSection.style.display = 'none';
    resultsSection.style.display = 'none';
    errorSection.style.display = 'none';
}

function resetToUpload() {
    hideAllSections();
    uploadSection.style.display = 'block';
    
    // Scroll back to upload section
    uploadSection.scrollIntoView({ behavior: 'smooth' });
}

function downloadResult() {
    if (!resultImageData) {
        showError('No result image available for download.');
        return;
    }

    // Convert base64 to blob and download
    const link = document.createElement('a');
    link.href = resultImageData;
    link.download = `panel_analysis_result_${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function exportData(format) {
    if (!resultImageData) {
        showError('No analysis data available for export.');
        return;
    }

    const analysisData = {
        timestamp: new Date().toISOString(),
        totalShapes: document.getElementById('shapesCount').textContent,
        scaleUsed: document.getElementById('scaleUsed').textContent,
        processingTime: document.getElementById('processingTime').textContent,
        imageResolution: document.getElementById('imageResolution').textContent,
        theoreticalPrecision: document.getElementById('theoreticalPrecision').textContent,
        recommendedUse: document.getElementById('recommendedUse').textContent,
        shapeBreakdown: getShapeBreakdownData()
    };

    switch (format) {
        case 'json':
            exportJSON(analysisData);
            break;
        case 'csv':
            exportCSV(analysisData);
            break;
        case 'pdf':
            exportPDF(analysisData);
            break;
    }
}

function getShapeBreakdownData() {
    const breakdown = {};
    const shapeItems = document.querySelectorAll('.shape-stat-item');
    shapeItems.forEach(item => {
        const shapeName = item.querySelector('.shape-name').textContent;
        const shapeCount = item.querySelector('.shape-count').textContent;
        breakdown[shapeName] = parseInt(shapeCount);
    });
    return breakdown;
}

function exportJSON(data) {
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `panel_analysis_${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function exportCSV(data) {
    let csvContent = 'Property,Value\n';
    csvContent += `Timestamp,${data.timestamp}\n`;
    csvContent += `Total Shapes,${data.totalShapes}\n`;
    csvContent += `Scale Used,${data.scaleUsed}\n`;
    csvContent += `Processing Time,${data.processingTime}\n`;
    csvContent += `Image Resolution,${data.imageResolution}\n`;
    csvContent += `Theoretical Precision,${data.theoreticalPrecision}\n`;
    csvContent += `Recommended Use,${data.recommendedUse}\n`;
    csvContent += '\nShape Breakdown:\n';
    csvContent += 'Shape Type,Count\n';

    Object.entries(data.shapeBreakdown).forEach(([shape, count]) => {
        csvContent += `${shape},${count}\n`;
    });

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `panel_analysis_${Date.now()}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

function exportPDF(data) {
    // For PDF export, we'll create a simple HTML report and use the browser's print functionality
    const reportWindow = window.open('', '_blank');
    const reportHTML = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Panel Analysis Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .section { margin: 20px 0; }
                .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .data-table th { background-color: #f2f2f2; }
                .image-container { text-align: center; margin: 20px 0; }
                .image-container img { max-width: 100%; height: auto; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Industrial Panel Analysis Report</h1>
                <p>Generated on: ${new Date().toLocaleString()}</p>
            </div>

            <div class="section">
                <h2>Analysis Summary</h2>
                <table class="data-table">
                    <tr><th>Property</th><th>Value</th></tr>
                    <tr><td>Total Shapes Detected</td><td>${data.totalShapes}</td></tr>
                    <tr><td>Scale Used</td><td>${data.scaleUsed}</td></tr>
                    <tr><td>Processing Time</td><td>${data.processingTime}</td></tr>
                    <tr><td>Image Resolution</td><td>${data.imageResolution}</td></tr>
                    <tr><td>Theoretical Precision</td><td>${data.theoreticalPrecision}</td></tr>
                    <tr><td>Recommended Use</td><td>${data.recommendedUse}</td></tr>
                </table>
            </div>

            <div class="section">
                <h2>Shape Breakdown</h2>
                <table class="data-table">
                    <tr><th>Shape Type</th><th>Count</th></tr>
                    ${Object.entries(data.shapeBreakdown).map(([shape, count]) =>
                        `<tr><td>${shape}</td><td>${count}</td></tr>`
                    ).join('')}
                </table>
            </div>

            <div class="section">
                <h2>Annotated Result</h2>
                <div class="image-container">
                    <img src="${resultImageData}" alt="Annotated Panel Analysis">
                </div>
            </div>
        </body>
        </html>
    `;

    reportWindow.document.write(reportHTML);
    reportWindow.document.close();

    // Wait for images to load, then print
    setTimeout(() => {
        reportWindow.print();
    }, 1000);
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Add some visual feedback for better UX
function addRippleEffect(element, e) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');
    
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Add ripple effect to buttons
document.querySelectorAll('button').forEach(button => {
    button.addEventListener('click', function(e) {
        if (!this.disabled) {
            addRippleEffect(this, e);
        }
    });
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    button {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
